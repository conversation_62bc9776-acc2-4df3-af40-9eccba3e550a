{"cells": [{"cell_type": "code", "execution_count": 1, "id": "86c5f73d-0e38-434a-8cda-391661f11339", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from azure.cosmos import CosmosClient, PartitionKey\n", "from dotenv import load_dotenv\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.messages import SystemMessage, HumanMessage\n", "from time import time\n", "from uuid import UUID\n", "\n", "os.chdir(\"C:\\\\Users\\\\<USER>\\\\OneDrive - Metyis\\\\Documenten\\\\Personal Documents\\\\LegalPA\\\\statuta-rag-api-v2\\\\\")\n", "load_dotenv(\"src/.env\")\n", "\n", "from src.app.cosmos_db_client import CosmosDBClient, _create_context_prompt_from_document\n", "from src.app.services.chat.chat_services_v1 import ChatService\n", "from src.app.services.completion.completion_services_v1 import DocumentCompletionService, ProjectCompletionService\n", "from src.app.models.chat.chat_models_v1 import ChatRequest, ChatResponse, StreamingChunk\n", "from src.app.services.rechtspraak_search_service import RechtspraakSearchService\n", "from src.app.models.projects.completion_models_v1 import (\n", "    CompletionOptions,\n", "    CompletionType,\n", ")\n", "from IPython.display import Markdown, display"]}, {"cell_type": "markdown", "id": "5ec5cc2b-4cd0-41f6-aab4-ec02f9086784", "metadata": {}, "source": ["# Test Cosmos-db Retrieval"]}, {"cell_type": "markdown", "id": "f156aee5-ff22-4d46-b95e-dbd82701946c", "metadata": {}, "source": ["### From the actual cosmos db"]}, {"cell_type": "code", "execution_count": 5, "id": "ecd28f80-9650-44dd-8eb4-10d3c7777f8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 5 documents\n"]}, {"data": {"text/plain": ["{'id': 'bdf112b1-39c8-48e8-b154-ad0b4f109d36',\n", " 'Id': 'bdf112b1-39c8-48e8-b154-ad0b4f109d36',\n", " 'OrganizationId': '52c0808e-8d6c-4094-bcb8-b3abc582ae30',\n", " 'ProjectId': '9f8819b4-2e8e-4ba8-9a2d-26f9cb6072e4',\n", " 'Name': 'Chat_2_Rechtsgebieden_en_grenzen.docx',\n", " 'Category': 'ClientInformation',\n", " 'Type': 'ClientResponse',\n", " 'IsAnonymized': True,\n", " 'FileFormat': 1,\n", " 'PageCount': 0,\n", " 'ParagraphCount': 0,\n", " 'Paragraphs': None,\n", " '_rid': 'LLF9AK--3Y4BAAAAAAAAAA==',\n", " '_self': 'dbs/LLF9AA==/colls/LLF9AK--3Y4=/docs/LLF9AK--3Y4BAAAAAAAAAA==/',\n", " '_etag': '\"0a003bd3-0000-4700-0000-686b75860000\"',\n", " '_attachments': 'attachments/',\n", " '_ts': 1751872902}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"org-52c0808e-8d6c-4094-bcb8-b3abc582ae30\"\n", "project_id = \"prj-9f8819b4-2e8e-4ba8-9a2d-26f9cb6072e4\"\n", "document_ids = [\"bdf112b1-39c8-48e8-b154-ad0b4f109d36\", \"94da9bdf-46eb-4523-8c4f-beed403a1579\"]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "documents = cosmosdb.get_all_documents()\n", "print(f\"Found {len(documents)} documents\")\n", "documents[0]"]}, {"cell_type": "markdown", "id": "f663eb68-4c9c-4545-b2b6-8ec297f43bad", "metadata": {}, "source": ["### Fallback to retriever for old projects"]}, {"cell_type": "code", "execution_count": 6, "id": "db8ca75e-a6f5-4c45-9795-817b214aaf3c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 100 documents\n"]}, {"data": {"text/plain": ["{'id': 'YzY4NzE2N2YtNGQ2OS00ZjE5LWFhZjMtNmU3Njk5YjQ1YzUz',\n", " 'OrganizationId': '46e06422-585a-4986-bf0e-f4ae9b84897e',\n", " 'ProjectId': '77344fb2-5676-49d4-8a9a-4d6454ee84cd',\n", " 'DocumentId': '82450b24-0cc6-4433-9a12-afff56a01991',\n", " 'Name': 'Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwij<PERSON>).pdf',\n", " 'PageCount': 6,\n", " 'Paragraphs': [{'paragraph_number_in_document': 1,\n", "   'paragraph_number_on_page': 1,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': \"voeg/mem/A vao, dearodc ADVOCATEN Rechtbank Gelderland locatie Arnhem Zittingsdatum: 11 maart 2024 Zaakkenmerk: C/05/421257 Rolnummer: 23/287 AKTE HOUDENDE OVERLEGGING PRODUCTIES inzake: HOOIMEIJER HOLDING B.V. zetelende te: tgk875kg61 gedaagde advocaten: mr. J.N.R<PERSON> en mr. D.B.J. Reg<PERSON> contra: JUST4SAFETY B.V. gevestigd te: Tiel eiseres advocaat: mr. <PERSON><PERSON>, Hooimeijer legt hierbij de volgende aanvullende producties over ten behoeve van de mondelinge behandeling van 11 maart 2024, inclusief een bijgewerkt productieoverzicht. Tijdens de mondelinge behandeling zal Hooimeijer zich nader uitlaten over deze producties. Hieronder wordt beknopt de inhoud van de producties toegelicht. Productie 64: Printscreens van de website www.just4medical.nl Uit randnummer 46 en verder van de conclusie van antwoord tevens eis in reconventie blijkt dat Just4Safety (maar ook Van Wijk en De Bruin, zie dagvaarding Hooimeijer) in strijd handelen met de geheimhoudingsovereenkomst door op verschillende websites het ondernemingsplan van Hooimeijer te exploiteren. Het betreft de website www.just4safety.nl (Productie 50), www.just4carehospitality.nl (Productie 52), www.ehbo-shop.nl (Productie 53), www.ikwilmijlatentesten.nu/just4safety (Productie 55) en www.one2test.nl (Productie 56). Recentelijk heeft Hooimeijer geconstateerd dat Just4Safety, Van Wijk en De Bruin in aanvulling op voornoemde websites tevens inbreuk maken op de volegmainn valk d.earoek ADVOCATEN geheimhoudingsovereenkomst door op de website www.just4medical.nl tot op heden nog steeds coronasneltesten aan te bieden. Als productie 64 wordt hierbij een recente printscreen van de website van www.just4medical.nl ingebracht. Productie 65: Algemene voorwaarden Just4, versie december 2023 Naast dat uit de reeds aangehaalde webpagina's blijkt dat Just4Safety, Van Wijk en De Bruin inbreuk maken op de geheimhoudingsovereenkonnst, volgt ook uit artikel 14.3 van de meest recente versie van de algemene voorwaarden van Just4Safety - die nota bene drie maanden geleden zijn herzien - dat Just4Safety, en dus Van Wijk en De Bruin, kennelijk nog steeds het afnemen van coronasneltesten als dienst aanbiedt: 14.3 Just4 garandeert niet dat het resultaat van de verleende Dienst, waaronder,\"},\n", "  {'paragraph_number_in_document': 2,\n", "   'paragraph_number_on_page': 2,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': \"maar niet beperkt tot het COVID- testresultaat, te allen tijde betrouwbaar is en staat niet in voor een juiste overbrenging van de inhoud van een door of namens Just4 verzonden e-mail, noch voor de tijdige ontvangst daarvan. Deze versie van de algemene voorwaarden heeft de naam 'Algemene voorwaarden Just4' en worden gebruikt op de websites www.just4safety.nl, www.just4carehospitality.ni en www.just4medical.nl, allen websites die gelieerd zijn aan Just4Safety (en dus ook aan Van Wijk en De Bruin). Deze zaak wordt behandeld door: mr. J<PERSON><PERSON><PERSON><PERSON><PERSON> mr. D.B.J. Regterschot Postbus 1126 Postbus 1126 6501 BC tgk875kg61 6501 BC tgk875kg61 T: +31 24 3810 886 T: +31 24 3810 825 E: j<PERSON>@pvdb.nl E: d.re<PERSON><PERSON><PERSON><PERSON>@pvb.nl vocuineuto, \\\\cut d.<PERSON><PERSON><PERSON>, ADVOCATEN PRODUCTIEOVERZICHT Productie 1 Uittreksel uit het Handelsregister van de Kamer van Koophandel van Hooimeijer Holding B.V. Productie 2 Uittreksel uit het Handelsregister van de Kamer van Koophandel van Just4Safety B.V. Productie 3 Uittreksel uit het Handelsregister van de Kamer van Koophandel van 3ust4 B.V. Productie 4 Oprichtingsakte van Just4Safety B.V. Productie 5 Uittreksel uit het Handelsregister van de Kamer van Koophandel van Just4Safety V.O.F. Productie 6 E-mail van de heer Hooimeijer aan Van Wijk van Just4Safety V.O.F. van 17 oktober 2020, 12.06 uur Productie 7 E-mail van Van Wijk aan de heer Hooimeijer van 17 oktober 2020 om 12.16 uur Productie 8 E-mail van de heer Hooimeijer aan Van Wijk van 17 oktober 2020 Productie 9 Facturen van Just4Safety aan Hooimeijer Holding B.V. Productie 10 Overzicht van de gedane investringen door Hooimeijer Holding B.V. gecontroleerd door de accountant Productie 11 Whatsapp-correspondentie 20 december 2021 Productie 12 Kopie website arboportaal inzake financiële tegemoetkoming voor werkgevers bij het sneltesten Productie 13 Uittreksel uit het Handelsregister van de Kamer van Koophandel van Civerz B.V. (Convad) Productie 14 Uittreksel uit het Handelsregister van de Kamer van Koophandel Sangster Productie\"},\n", "  {'paragraph_number_in_document': 3,\n", "   'paragraph_number_on_page': 3,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': '15 Uittreksel uit het Handelsregister van de Kamer van Koophandel van Health Innprovement Company B.V. Productie 16 Uittreksels uit het Handelsregister van de Kamer van Koophandel van GrowthPond B.V. en Merity B.V. Productie 17 Overeenkomst tussen HIC en NexusLabor van 9 maart 2021 Productie 18 Verklaring Van de Poppe van 29 juni 2023 Productie 19 Diverse Whatsapp-berichten inzake inzet de heer Hooimeijer t- te.uncunn \\\\cut d.ear<PERSON><PERSON>, ADVOCATEN Productie 20 E-mail van de heer Hooimeijer aan mevrouw Sohilait Productie 21 Verzoeken van mevrouw Sohilait per Whatsapp om rekening afschriften te ontvangen van 14 februari 2022 en 4 juli 2022 Productie 22 Whatsapp-bericht van 2 november 2021 van de heer Hooimeijer over contact Sohilait met incassobureau Productie Uittreksel uit het Handelsregister van de Kamer van 23 Koophandel van Coronasneltestlijn B.V. E-mail van de heer De Vos van NexusLabor van 5 april Productie 24 2021 Conceptovereenkomst van Coronasneltestlijn B.V. Productie 25 Whatsapp-bericht van de heer Hooimeijer aan de heer Van Productie 26 Wijk van 3 mei 2021 Productie 27 E-mail van HIC aan NexusLabor van 17 mei 2021 Productie 28 Whatsapp-bericht van 3 mei 2021. Productie 29 E-mail van 25 Page 1 of 6'}]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "documents = cosmosdb.get_all_documents()\n", "print(f\"Found {len(documents)} documents\")\n", "documents[0]"]}, {"cell_type": "code", "execution_count": 2, "id": "362dd6f7-be34-4e2e-aa32-4ec1b097de69", "metadata": {}, "outputs": [], "source": ["#cosmosdb.create_context_for_llm_prompt()"]}, {"cell_type": "markdown", "id": "1ae5a5ba-b5c3-48a9-a5f8-************", "metadata": {}, "source": ["# Test timeline"]}, {"cell_type": "markdown", "id": "15f274d6-09b2-451b-9c5f-11bd138c93c3", "metadata": {}, "source": ["### Document(s) Timeline"]}, {"cell_type": "code", "execution_count": null, "id": "c13db98a-b619-4b34-b1d7-5de98ff37eab", "metadata": {}, "outputs": [], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "#documents = cosmosdb.get_all_documents()\n", "#context_list = cosmosdb.create_context_for_iterative_llm_prompting()\n", "\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.TIMELINE)\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "398e6408-9c2e-4136-9248-704c1db9e7e9", "metadata": {}, "source": ["### Project Timeline"]}, {"cell_type": "code", "execution_count": null, "id": "59801261-f599-41cb-87d8-5997adecb4fc", "metadata": {"scrolled": true}, "outputs": [], "source": ["from time import time\n", "t0 = time()\n", "project_service = ProjectCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await project_service.get_completion(input_text='', completion_options=CompletionOptions(completion_type=CompletionType.TIMELINE))\n", "print(f\"Generating timeline took {(time() - t0) / 60: .1f} minutes\")\n", "display(Markdown(foo.content))\n"]}, {"cell_type": "markdown", "id": "017b4cc0-8cbe-4815-8659-43f5468c6a10", "metadata": {}, "source": ["# Test Summary"]}, {"cell_type": "markdown", "id": "027df23f-ca5a-4eeb-ad59-19a9fc745a77", "metadata": {}, "source": ["### Single Document"]}, {"cell_type": "code", "execution_count": 2, "id": "21a7534e-e1c6-4055-83e2-5ee015fc5856", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Container for project 77344fb2-5676-49d4-8a9a-4d6454ee84cd in organization 46e06422-585a-4986-bf0e-f4ae9b84897e not found. Falling back to using the retriever\n"]}, {"data": {"text/markdown": ["### <PERSON><PERSON><PERSON><PERSON> van het document\n", "\n", "#### **Hoofdgedachte**\n", "Het document betreft een akte van overlegging van aanvullende producties door Hooimeijer Holding B.V. in een juridische procedure tegen Just4Safety B.V. Het centrale thema is de stelling van Hooimeijer dat Just4Safety, samen met betrok<PERSON><PERSON> Van Wijk en De Bruin, inbreuk maakt op een geheimhoudingsovereenkomst. Deze inbreuken zouden blijken uit het exploiteren van Hooimeijers ondernemingsplan via meerdere websites en uit specifieke bepalingen in de algemene voorwaarden van Just4Safety.\n", "\n", "---\n", "\n", "#### **Belangrijkste punten**\n", "1. **Over<PERSON><PERSON> van de geheimhoudingsovereenkomst**  \n", "   - Just4Safety en de betrokken partijen zouden vertrouwelijke informatie, waaronder het ondernemingsplan van Hooimeijer, he<PERSON><PERSON> geëxploiteerd via verschillende websites, waaronder www.just4medical.nl. (Zie productie 64).\n", "   - Nieuwe printscreens van de website www.just4medical.nl worden ingebracht als bewijs van de voortdurende inbreuk.\n", "\n", "2. **Algemene voorwaarden van Just4Safety**  \n", "   - Uit de meest recente versie van de algemene voorwaarden van Just4Safety (december 2023) blijkt dat zij nog steeds diensten aanbieden die verband houden met coronasneltesten, wat volgens Hooimeijer ook een schending van de geheimhoudingsovereenkomst inhoudt. Dit wordt ondersteund door artikel 14.3 van de algemene voorwaarden (zie productie 65).\n", "\n", "3. **Overzicht van ingebrachte producties**  \n", "   - Het document bevat een uitgebreid productieoverzicht (producties 1-65), wa<PERSON>n bewijsstukken worden gepresenteerd die variëren van correspondentie en overeenkomsten tot printscreens en financiële gegevens. Deze producties ondersteunen Hooimeijers beweringen over de schending van de geheimhoudingsovereenkomst.\n", "\n", "4. **Relevante juridische principes**  \n", "   - De geheimhoudingsovereenkomst tussen de partijen vormt de kern van het geschil. Het document benadrukt de verplichting tot vertrouwelijkheid en de gevolgen van schen<PERSON>, zoals schadeclaims en mogelijke boetes.\n", "   - Artikel 14.3 van de algemene voorwaarden van Just4Safety wordt aangehaald om aan te tonen dat Just4 haar verplichtingen jegens Hooimeijer niet naleeft.\n", "\n", "---\n", "\n", "#### **St<PERSON><PERSON><PERSON><PERSON> van de documenten**\n", "De documenten zijn gestructureerd als volgt:\n", "- **Akte van overlegging**: De akte bevat de introductie en toelichting op de aanvullende producties.\n", "- **Productieoverzicht**: <PERSON><PERSON> lij<PERSON> van alle ingebrachte producties (1-65), die dienen als bewijsstukken voor de claims van Hooimeijer.\n", "- **Uitwerking producties 64 en 65**: Details over de schendingen via de website www.just4medical.nl en de algemene voorwaarden van Just4Safety.\n", "- **Algemene voorwaarden (bijlage)**: <PERSON><PERSON> volledige tekst van de algemene voorwaarden van Just4Safety, die relevant zijn voor de beoordeling van de <PERSON>aa<PERSON>.\n", "\n", "---\n", "\n", "#### **Inconsistenties**\n", "Er worden in het document geen expliciete inconsistenties of tegenstrijdigheden genoemd. De aangevoerde producties lijken een samenhangend betoog te vormen, waarin bewijsstuk<PERSON> el<PERSON>ar onders<PERSON>unen.\n", "\n", "---\n", "\n", "#### **Aanbevolen leesvolgorde**\n", "1. **A<PERSON><PERSON> van overlegging (pagina 1)**: Dit geeft een overzicht van het doel van de aanvullende producties en de centrale claims.\n", "2. **<PERSON><PERSON> 64**: Printscreens van www.just4medical.nl, die de inbreuk op de geheimhoudingsovereenkomst aantonen.\n", "3. **<PERSON><PERSON> 65**: De algemene voorwaarden van Just4Safety, die verdere ondersteuning bieden voor de schendingen.\n", "4. **Productieoverzicht**: Voor een volledig beeld van de overige bewijsmaterialen.\n", "5. **Algemene voorwaarden (volledige tekst)**: Voor een gedetailleerde juridische analyse.\n", "\n", "---\n", "\n", "### Con<PERSON><PERSON>ie\n", "Het document biedt een uitgebreide onderbouwing van de claims van Hooimeijer tegen Just4Safety, met een focus op schendingen van een geheimhoudingsovereenkomst en de rol van de algemene voorwaarden van Just4Safety. Het bewijs wordt gestructureerd gepresenteerd via een akte en een productieoverzicht, waarbij de producties 64 en 65 de kern vormen van het argument."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.DOCUMENT_SUMMARY)\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "9248d692-ea5f-444a-b003-34346f90e998", "metadata": {}, "source": ["### Multiple Documents"]}, {"cell_type": "code", "execution_count": 3, "id": "d85f763a-0f1c-451a-a81c-7d6fcdd16ef6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Container for project 77344fb2-5676-49d4-8a9a-4d6454ee84cd in organization 46e06422-585a-4986-bf0e-f4ae9b84897e not found. Falling back to using the retriever\n"]}, {"data": {"text/markdown": ["### <PERSON><PERSON><PERSON><PERSON>\n", "\n", "#### Ho<PERSON><PERSON><PERSON><PERSON>\n", "Het centrale thema van de documenten betreft het juridische geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V., waari<PERSON> stelt dat Just4Safety meerdere contractuele verplichtingen heeft geschonden, waaronder schending van een geheimhoudingsovereenkomst en onrechtmatig handelen met betrekking tot het aanbieden van coronasneltesten. Daarnaast worden diverse aanvullende producties ingebracht om de vorderingen van Hooimeijer te onderbouwen, waaronder bewijsstukken voor beslagleggingen, contracten en communicatie tussen partijen.\n", "\n", "---\n", "\n", "#### Belangrijkste punten\n", "\n", "1. **<PERSON><PERSON><PERSON> <PERSON>hou<PERSON>sovereenko<PERSON>t:**\n", "   - Hooimeijer beschuldigt Just4Safety van het delen en exploiteren van vertrouwelijke informatie, zoals het ondernemingsplan, op verschillende websites, waaronder just4medical.nl (Productie 64). Dit zou een directe schending van de geheimhoudingsovereenkomst inhouden.\n", "   - Artikel 14.3 van de algemene voorwaarden van Just4Safety (versie december 2023) wordt genoemd als bewijs dat Just4 nog steeds coronasneltesten aanbiedt, wat in strijd z<PERSON> zijn met e<PERSON><PERSON> afspra<PERSON> (Productie 65).\n", "\n", "2. **Conservatoire beslagleggingen:**\n", "   - Hooimeijer heeft conservatoir beslag laten leggen op de tegoeden van Just4Safety bij verschillende banken, waaronder ABN Amro, Rabobank, ING en de Volksbank (Producties 59-60). Deze beslagleggingen dienen ter zekerheidstelling van een vordering van € 246.507,92.\n", "   - De derdenverklaringen van de banken tonen aan dat er slechts beperkte tegoeden beschikbaar zijn (bijvoorbeeld € 6.712,81 bij Rabobank na verrekening).\n", "\n", "3. **Onderbouwing van aansprakelijkheid:**\n", "   - Producties zoals e-mails, Whatsapp-berichten en overeenkomsten (bijv. Producties 61-63) worden gebruikt om aan te tonen dat Just4Safety verantwoordelijk was voor het indienen van subsidieaanvragen en facturatie, wat relevant is voor de schadeclaims.\n", "\n", "4. **Juridische context:**\n", "   - Het geschil wordt behandeld door de rechtbank Gelderland, locatie Arnhem, onder zaaknummer C/05/421257.\n", "   - De beslagleggingen zijn uitgevoerd op basis van artikel 718 Rv (Wetboek van Burgerlijke Rechtsvordering), en Just4Safety wordt verplicht om gegevens over haar tegoeden te verstrekken.\n", "\n", "---\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON><PERSON> van de documenten\n", "\n", "1. **Documenten en producties:**\n", "   - De documenten bestaan voornamelijk uit processtukken zoals aktes van overlegging van aanvullende producties, beslagstukken, algemene voorwaarden, communicatie tussen partijen, en verklaringen van derden.\n", "   - Elk document bevat een overzicht van de ingebrachte producties en een toelichting op de relevantie ervan in de context van het geschil.\n", "\n", "2. **B<PERSON><PERSON><PERSON><PERSON> aan het hoofdthema:**\n", "   - De aktes van overlegging (bijv. Zitting_-_240227 en Zitting_-_231219) structureren de ingebrachte bewijsmaterialen en koppelen deze aan specifieke juridische claims.\n", "   - Beslagstukken (Producties 59-60) onderbouwen de financiële vorderingen van Hooimeijer.\n", "   - Algemen<PERSON> (Productie 65) en printscreens van websites (Productie 64) ondersteunen de beschuldigingen van schending van de geheimhoudingsovereenkomst.\n", "\n", "---\n", "\n", "#### Inconsistenties\n", "\n", "Er lijken geen directe tegenstrijdigheden binnen de documenten te zijn. Wel is er een mogelijk verschil in interpretatie tussen partijen over de verantwoordelijkheden met betrekking tot het indienen van subsidies (Productie 62). Dit verschil kan van invloed zijn op de beoordeling van aansprakelijkheid.\n", "\n", "---\n", "\n", "#### Aanbevolen leesvolgorde\n", "\n", "1. **Zitting_-_240227_Akte_aanvullende_producties_J4S:**\n", "   - Dit document bevat de meest recente producties en legt de nadruk op de kernclaims, zoa<PERSON> de schending van de geheimhoudingsovereenkomst.\n", "2. **Zitting_-_231219_Akte_houdende_overlegging_aanvullende_producties_J4S:**\n", "   - Dit document biedt aanvullende context over de beslagleggingen en financiële onderbouwing.\n", "3. **Producties 64 en 65:**\n", "   - <PERSON><PERSON> zijn direct relevant voor de beschuldigingen met betrekking tot de schending van de geheimhoudingsovereenkomst.\n", "4. **Beslagstukken en derdenverklaringen (Producties 59-60):**\n", "   - <PERSON><PERSON> onderbouwen de financiële vorderingen en de status van het beslag.\n", "\n", "---\n", "\n", "#### Con<PERSON><PERSON>ie\n", "\n", "De documenten bieden een gestructureerd overzicht van de juridische claims en bewijsmaterialen in het geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. De nadruk ligt op schendingen van de geheimhoudingsovereenkomst, de financiële gevo<PERSON>, en de juridische acties die Hooimeijer heeft ondernomen. Het bewijsmateriaal lijkt grotendeels consistent en ondersteunt de kernclaims van Hooimeijer."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from uuid import UUID\n", "organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.DOCUMENT_SUMMARY)\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "0c2337dd-1b05-400b-a5ca-e70fc47e59ff", "metadata": {}, "source": ["# Search Case Law"]}, {"cell_type": "markdown", "id": "a4909253-e67b-4194-9b83-d17a4380507d", "metadata": {}, "source": ["### Return IDs only"]}, {"cell_type": "code", "execution_count": 3, "id": "69fec52b-01d7-4695-a0f9-8a01d303c242", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\OneDrive - Metyis\\Documenten\\Personal Documents\\LegalPA\\statuta-rag-api-v2\\src\\app\\services\\rechtspraak_search_service.py:84: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  response = self._chat_client([\n"]}, {"data": {"text/plain": ["['ECLI:NL:GHAMS:2023:1485',\n", " 'ECLI:NL:RBZWO:2004:AR2772',\n", " 'ECLI:NL:RBNHO:2024:11205',\n", " 'ECLI:NL:GHSHE:2023:3785',\n", " 'ECLI:NL:GHARN:2009:BJ0914',\n", " 'ECLI:NL:RBNNE:2024:1576',\n", " 'ECLI:NL:RBDHA:2022:13604',\n", " 'ECLI:NL:RBAMS:2012:BW2924',\n", " 'ECLI:NL:RBARN:2010:BO2136',\n", " 'ECLI:NL:RBMNE:2014:6750']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["query=\"<PERSON><PERSON><PERSON> go<PERSON>e onverwacht de deur van zijn bestelbusje open waarna client er op volle snelheid tegenaan is gefietst, waardoor hij is gevallen en een hersenschudding en een gebroken arm heeft.\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=True)\n", "relevant_legal_cases"]}, {"cell_type": "markdown", "id": "3b961d1c-a581-4c66-b117-da47dfb0640f", "metadata": {}, "source": ["### Full text of cases"]}, {"cell_type": "code", "execution_count": 4, "id": "c80206a7-83b0-4605-85d8-0336c3a16927", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unclosed client session\n", "client_session: <aiohttp.client.ClientSession object at 0x00000278D2835460>\n", "Unclosed connector\n", "connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x00000278D2908230>, 47722.031)]']\n", "connector: <aiohttp.connector.TCPConnector object at 0x00000278D2837A70>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Enchanced query used for semantic search: kleinschalige wietplantage, 10 planten, geen groot<PERSON>el, geen energiediefstal, strafrechtelijke vervolging, he<PERSON><PERSON><PERSON><PERSON>, gering<PERSON> ho<PERSON>, persoonlijke gebruik, stra<PERSON><PERSON><PERSON>, rechterlijke uitspraken\n", "\n", "Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:GHAMS:2020:2419, Gerechtshof Amsterdam, 08-09-2020, 23-004205-18.\n", "Case Summary: Medeple<PERSON> (344 planten), vrijspraak diefstal elektriciteit..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNNE:2021:5376, Rechtbank Noord-Nederland, 21-12-2021, 18/117972-21.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON><PERSON>, grote ho<PERSON>heid planten. \n", "Gelet op de wijze waaro<PERSON> de hennepkwekerij was opgezet en het aantal aanwezige hennepplanten was sprake van een zekere mate van  professionaliteit met betrekking tot de hennepkwekerij. Verdachte heeft bijgedragen aan het in stand houden van het illegale hennepcircuit. Door de productie van hennepplanten wordt de volksgezondheid in gevaar gebracht en verd....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2018:4754, Rechtbank Rotterdam, 09-05-2018, 10/038099-18 / Raadkamernummer 18/762.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> he<PERSON> geteeld voor eigen medicinaal gebruik. 552a Sv-beklag niet ontvankelijk. Ook kleinschalige teelt is strafbaar..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2014:6456, Rechtbank Rotterdam, 25-07-2014, KTN-2498346.\n", "Case Summary: <PERSON><PERSON><PERSON> vraag of er sprake is van hennepteelt voor eigen gebruik dan wel van beroep<PERSON>- of bedrijfsmatige hennepteelt is van belang hoeveel planten (bij gedaagde zijn 3 à 4 planten aangetroffen) maar ook welke apparatuur in de betreffende ruimte is aangetroffen.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3039, Rechtbank Midden-Nederland, 28-07-2022, 16/707304-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft in de periode van 1 maart 2015 tot en met 9 oktober 2018 als groothandel een grote hoeveelheid voorwerpen en stoffen, die bestemd zijn voor de illegale hennept<PERSON>t, voor<PERSON><PERSON> gehad, te koop a<PERSON>, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hiermee heeft verdachte zich ruim drie en een half jaar schuldig gemaakt aan overtreding van artikel 11a van de Opiumwet. Daarnaast h....\n", "Full text: \n", "\n", "Case: ECLI:NL:GHDHA:2022:1475, Gerechtshof Den Haag, 03-08-2022, **********.\n", "Case Summary: Strafrechtelijke vervolging voor hennepteelt in woning en diefstal stroom, terwijl eerder een bestuurlijke boete was opgelegd voor het zonder vergunning onttrekken van woonruimte aan de woningvoorraad, ten behoe<PERSON> van hennepteelt. \n", "Het hof is van oordeel dat geen sprake is van ‘hetzelfde feit’ als bedoeld in art. 68 Sr, reeds gelet op de juridische aard van de feiten.\n", "Officier van justiti<PERSON> is o....\n", "Full text: \n", "\n", "Case: ECLI:NL:GHAMS:2015:3786, Gerechtshof Amsterdam, 20-07-2015, 23-002034-14.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON> ho<PERSON><PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON><PERSON>.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3055, Rechtbank Midden-Nederland, 28-07-2022, 16/706555-18 (P).\n", "Case Summary: Verd<PERSON><PERSON> is vrijgesproken van het ten laste gelegde nu niet kan worden bewezen dat hij feitelijk leiding heeft gegeven aan een groothandel die in de periode van 1 maart 2015 tot en met 9 oktober 2018 als groothandel een grote hoeveelheid voorwerpen en stoffen, die bestemd zijn voor de illegale hennepteelt, voorhanden gehad, te koop a<PERSON>eboden, verkocht, afgeleverd, verstrekt en vervoerd. Er ka....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3052, Rechtbank Midden-Nederland, 28-07-2022, 16/705044-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft samen met een ander feitelijk leiding gegeven aan een groothandel, welk bedrijf een grote hoeveelheid voorwerpen en stoffen, die bestemd waren voor de illegale hennepteelt, voor<PERSON><PERSON> had, te koop heeft aangeboden, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hi<PERSON><PERSON> heeft verdachte samen met een ander leiding gegeven aan een bedrijf dat zich ruim drie en een half jaar schuldig ....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3053, Rechtbank Midden-Nederland, 28-07-2022, 16/705045-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft samen met een ander feitelijk leiding gegeven aan een groothandel, welk bedrijf een grote hoeveelheid voorwerpen en stoffen, die bestemd waren voor de illegale hennepteelt, voor<PERSON><PERSON> had, te koop heeft aangeboden, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hi<PERSON><PERSON> heeft verdachte samen met een ander leiding gegeven aan een bedrijf dat zich ruim drie en een half jaar schuldig ....\n", "Full text: \n", "\n", "\n"]}], "source": ["query=\"kleinschalige wietplantage met slechts 10 planten, geen groothandel geen energiediefstal\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "rechtspraak_service.update_project_context(project_context_str)\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=False)\n", "print(f\"Enchanced query used for semantic search: {rechtspraak_service.semantic_search_query}\\n\")\n", "relevant_legal_cases_context_str = rechtspraak_service.format_search_results_for_context_prompt(documents=relevant_legal_cases)\n", "print(relevant_legal_cases_context_str)"]}, {"cell_type": "markdown", "id": "6e3d0c5f-0624-4453-8e00-d49164c78224", "metadata": {}, "source": ["### Enhance query with project context "]}, {"cell_type": "code", "execution_count": 4, "id": "8c8b995e-b882-4e85-94c1-1d594d1a7e41", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:asyncio:Unclosed client session\n", "client_session: <aiohttp.client.ClientSession object at 0x00000181B4FE6240>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Enchanced query used for semantic search: geheimhoudingsovereenkomst Just4Safety Hooimeijer schending boetebeding juridische geschillen samenwerking coronasneltest subsidieaanvragen financiële afwikkeling aansprakelijkheid NexusLabor CONVAD bedrijfsarts samenwerkingsovereenkomst teststraten\n", "\n", "Answering request took  0.1 minutes\n", "Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:RBGEL:2024:3508, Rechtbank Gelderland, 05-06-2024, 426586.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> met el<PERSON><PERSON> vier coronasneltestlocaties geëxploiteerd. <PERSON>  beëindigng van de samenwerking moet er tussen partijen worden afgerekend. Daarbij ontstaat het geschil. Eiseres vordert nog een bedrag van geda<PERSON>den, onder meer uit hoofde van schending van een geheimhoudingsovereenkomst. Die schending komt niet vast te staan. Ook de andere onderdelen van de vordering worden afgewezen..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2022:5738, Rechtbank Noord-Holland, 18-05-2022, 9536931.\n", "Case Summary: <PERSON><PERSON><PERSON> geheimhoudingsovereenkomst, onrechtmatige concurrentie.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBDHA:2022:15097, Rechtbank Den Haag, 07-12-2022, C/09/607435 / HA ZA 21-163.\n", "Case Summary: <PERSON><PERSON><PERSON> samenwerkingsovereenkomst en geheimhoudingsovereenkomst. Ook is sprake van een onrechtmatige daad. Eiseres heeft daardoor schade geleden. Toewijzing vorderingen. Verwijzing naar schadestaat. De gevorderde boete is niet toe<PERSON>..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2022:7077, Rechtbank Gelderland, 21-12-2022, C/05/387289 / HZ ZA 21-151.\n", "Case Summary: Samenwerkingsovereenkomst Covid19-testen en -teststraten. Borgstelling..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHSHE:2014:1276, Gerechtshof 's-Hertogenbosch, 06-05-2014, HD 200.121.637_01.\n", "Case Summary: aansprakelijkheid commanditair vennoot, b<PERSON><PERSON><PERSON><PERSON>.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2023:4839, Rechtbank Noord-Holland, 08-03-2023, C/15/329685 / HA ZA 22-401.\n", "Case Summary: Samenwerkingsovereenkomst; geen schending geheimhoudingsbeding en geen schending relatiebeding..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2023:5031, Rechtbank Rotterdam, 14-06-2023, C/10/648166 / HA ZA 22-926.\n", "Case Summary: Schadevergoedingsrecht. Overheersende arbeidsverhouding. Aanknoping bij dwingendrechtelijk kader van art. 7:650 BW leidt tot nietigheid boetebeding in separate geheimhoudingsovereenkomst. Verweten gedragingen zijn wel onrechtmatig. Schadebegroting..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHARL:2021:2476, Gerechtshof Arnhem-Lee<PERSON>en, 16-03-2021, 200.258.831/01.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> over financiële afwikkeling samenwerkingsovereenkomst. Uitleg overeenkomst..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHSHE:2017:3348, <PERSON>erechtshof 's-Hertogenbosch, 25-07-2017, 200 187 272_01.\n", "Case Summary: <PERSON><PERSON><PERSON> op boe<PERSON>beding in samenwerkingsovereenkomst vanwege het nalaten een gevelschildje te verwijderen nadat de overeenkomst was beëindigd. Matiging tot nihil..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2021:4326, Rechtbank Gelderland, 11-08-2021, C/05/376371 / HZ ZA 20-359.\n", "Case Summary: Financiële afwikkeling projectontwikkeling, uitleg samenwerkingsovereenkomst. Ontslag bestuurder is nietig..\n", "Full text: \n", "\n", "\n"]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "project_context_str = cosmosdb.create_context_for_llm_prompt()\n", "\n", "query=\"\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "rechtspraak_service.update_project_context(project_context_str=project_context_str)\n", "t0 = time()\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=False)\n", "print(f\"Enchanced query used for semantic search: {rechtspraak_service.semantic_search_query}\\n\")\n", "relevant_legal_cases_context_str = rechtspraak_service.format_search_results_for_context_prompt(documents=relevant_legal_cases)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes\")\n", "print(relevant_legal_cases_context_str)"]}, {"cell_type": "code", "execution_count": null, "id": "1d2ddd12-3ffd-4e24-9388-84a0f10170c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "320656a4-8963-42df-90a4-0a6302db3573", "metadata": {}, "source": ["# Test ChatServices"]}, {"cell_type": "markdown", "id": "ee78e050-9c2d-46a7-a323-c88049f030b2", "metadata": {}, "source": ["### Organization only"]}, {"cell_type": "code", "execution_count": 3, "id": "645c2c8e-2879-4c82-85b2-b27889b401ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Op dit moment is er geen project actief en dus geen specifieke zaak waarvan een beschrijving kan worden gegeven. Daarnaast is het zoeken naar relevante jurisprudentie niet ingeschakeld. Om relevante jurisprudentie te vinden, moet je eerst jurisprudentie toevoegen als bron via het menu 'Bronnen'.\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= None #\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "\n", "chat = ChatService(organization_id=organization_id,\n", "        project_id=project_id,\n", "        cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "            user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "            user_message=\"Can you give me a very short (3 sentences max) description of the current case? Also, did we already search for relevant case law or not?\")\n", "\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "response.content"]}, {"cell_type": "markdown", "id": "1eaba037-28e6-4430-a090-520eac8bf6da", "metadata": {}, "source": ["### With Project Context"]}, {"cell_type": "code", "execution_count": 4, "id": "17f6aa5f-2ae7-4745-a01d-6e3e90f1133a", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"De zaak betreft een geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. over vermeende schending van een geheimhoudingsovereenkomst en financiële afwikkeling van hun samenwerking bij coronasnelteststraten. Hooimeijer stelt dat Just4Safety vertrouwelijke informatie heeft gebruikt voor concurrerende activiteiten en vordert schadevergoeding en boetes. Er is nog geen relevante jurisprudentie gezocht; dit kan worden gedaan door jurisprudentie toe te voegen via het menu 'Bronnen'.\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id=\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "\n", "chat = ChatService(organization_id=organization_id,\n", "        project_id=project_id,\n", "        cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "            user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "            user_message=\"Can you give me a very short (3 sentences max) description of the current case? Also, did we already search for relevant case law or not?\")\n", "t0 = time()\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes.\")\n", "print(response.content)"]}, {"cell_type": "markdown", "id": "e44a7df8-6c77-4cc5-81ea-83692146b540", "metadata": {}, "source": ["### With Project Context & Case Law Context"]}, {"cell_type": "code", "execution_count": 2, "id": "26ab102a-b302-43c8-b043-1c7e20ec3ea6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for Relevant Case Law\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\OneDrive - Metyis\\Documenten\\Personal Documents\\LegalPA\\statuta-rag-api-v2\\src\\app\\services\\rechtspraak_search_service.py:84: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  response = self._chat_client([\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Answering request took  0.1 minutes\n"]}, {"data": {"text/markdown": ["**Besch<PERSON><PERSON><PERSON> van de huidige zaak:**  \n", "De zaak betreft een geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. Hooimeijer Holding B.V. stelt dat Just4Safety B.V., <PERSON><PERSON> met <PERSON> en De Bruin, inbreuk heeft gemaakt op een geheimhoudingsovereenkomst door vertrouwelijke informatie en bedrijfsplannen van Hooimeijer te exploiteren via verschillende websites. Dit omvat onder andere het aanbieden van coronasneltesten op websites zoals www.just4medical.nl. Daarnaast wordt gesteld dat Just4Safety zich niet heeft gehouden aan de geheimhoudingsverplichting zoals opgenomen in hun algemene voorwaarden. Hooimeijer heeft conservatoir beslag gelegd op tegoeden van Just4Safety bij diverse banken ter zekerheidsstelling van haar vordering. De zaak omvat ook een discussie over de kosten van het beslag en de verantwoordelijkheid voor subsidieaanvragen en facturatie.\n", "\n", "**<PERSON><PERSON> naar relevante jurisprudentie:**  \n", "<PERSON>a, er is al gezocht naar relevante jurisprudentie. Er zijn tien juridische zaken geïdentificeerd die mogelijk relevant zijn voor de huidige zaak. Indien aanvullende jurisprudentie nodig is, kan deze worden toegevoegd via het menu 'Bronnen'."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id=\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "\n", "chat = ChatService(\n", "    organization_id=organization_id,\n", "    project_id=project_id,          \n", "    cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(\n", "    session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "    user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "    user_message=\"Can you give me a brief description of the current case? Also, did we already search for relevant case law or not?\",\n", "    jurisprudence=True)\n", "t0 = time()\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes\")\n", "display(Markdown(response.content))"]}, {"cell_type": "code", "execution_count": 7, "id": "cf4727e9-dc13-4bb4-9879-f2286b0c5477", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:RBDHA:2017:8922, Rechtbank Den Haag, 08-08-2017, 17/818.\n", "Case Summary: Beklag ex artikel 552a Sv gegrond verkla<PERSON>. Geen machtiging rechter-commissaris voor conservatoir beslag. Een onrechtmatig gelegd conservatoir beslag op grond van artikel 94a Sv kan niet worden omgezet in een beslag op grond van artikel 94 Sv..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBOVE:2024:4970, Rechtbank Overijssel, 26-09-2024, C/08/319010 / KG ZA 24-168.\n", "Case Summary: Opheffing conservatoir beslag. Beslag onnodig. Intellectuele eigendom. Duurzaamheid en verspilling. Begrip \"belanghebbende\" in de zin van artikel 705 Rv. Waarheidsplicht..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2018:3577, Rechtbank Noord-Holland, 23-04-2018, 18/001883 en 18/001884.\n", "Case Summary: Beklag na conservatoir beslag in SFO. Onjuist is het standpunt dat het door de rechter-commissaris in de machtiging conservatoir beslag vermelde bedrag van € 339.819.16 een bovengrens voor de waarde van een beslag aangeeft. De wetgever heeft voor het conservatoir strafvorderlijk beslag de regeling van het conservatoir beslag in het Wetboek van Burgerlijke Rechtsvordering tot uitgangspunt genome....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2020:1496, Rechtbank Gelderland, 19-02-2020, C/05/366382 / KG ZA 20-67.\n", "Case Summary: kort geding. Opheffen conservatoir beslag. Internationaal geschil. Conservatoir beslag is in Nederland gelegd door partij uit Kroatïe. Bevoegdheid rechtbank..\n", "Full text: \n", "\n", "Case: ECLI:NL:OGEAM:2016:49, <PERSON><PERSON><PERSON> in eerste aanleg van Sint Maarten, 09-08-2016, AR 2014/175.\n", "Case Summary: <PERSON><PERSON><PERSON>t. Aansprakelijkheid van het schip versus persoonlijke aansprakelijkheid van de kapitein. Stelplicht. Conservatoir beslag..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBALM:2012:BV1683, Rechtbank Almelo, 17-01-2012, 381826 CV EXPL 4497/11.\n", "Case Summary: Betreft onbetaalde factuur, die onvoldoende gemotiveerd is betwist. Conservatoir beslag niet onrechtmatig. In reconventie vordering kosten van conservatoir derdenbeslag afgewezen.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBZWB:2023:7377, Rechtbank Zeeland-West-Brabant, 24-10-2023, C/02/412565 / KG ZA 23-388 (E).\n", "Case Summary: Vordering tot opheffing conservatoir beslag. Eisende partij heeft onvoldoende aannemelijk gemaakt dat de paardenwagen waarop het conservatoire beslag is gelegd zijn eigendo<PERSON> is. Vordering afgewezen..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2014:1974, Rechtbank Gelderland, 05-02-2014, 258624.\n", "Case Summary: Vordering tot opheffing van conservatoir beslag; onvoldoende aannemelijk dat geheimhoudingsovereenkomst is overtreden; summierlijk geble<PERSON> van de ondeugdelijkheid van de aan het beslag ten grondslag liggende vordering..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2022:739, Rechtbank Gelderland, 10-02-2022, C/05/398563 / KG ZA 22-6.\n", "Case Summary: <PERSON><PERSON> geding. Opheffing conservatoir beslag. Conservatoir beslag is reeds van rechtswege vervallen art. 704 lid 2 Rv., maar eiser heeft nog steeds voldoende belang bij veroordeling van gedaagde tot opheffing van het beslag..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2021:6947, Rechtbank Midden-Nederland, 03-12-2021, C/16/528804 / KL ZA 21-271.\n", "Case Summary: Executiegeschil. Opheffen conservatoir beslag..\n", "Full text: \n", "\n", "\n"]}], "source": ["print(chat.relevant_legal_cases_context_str)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}