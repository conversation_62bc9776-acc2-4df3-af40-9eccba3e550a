"""
This module defines services for chat completion operations.
"""

# Standard imports
import logging
import os
import uuid
from typing import AsyncGenerator, Optional
from uuid import UUID

# External imports
from langchain_community.chat_message_histories.cosmos_db import (
    CosmosDBChatMessageHistory,
)

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_openai import AzureChatOpenAI

# Internal imports
from src.app.models.chat.chat_models_v1 import ChatRequest, ChatResponse, StreamingChunk
from src.app.models.context_models_v1 import Context
from src.app.prompts.system.system_prompts_v1 import (CHAT_SERVICES_SYSTEM_PROMPT,
                                                      SEMANTIC_SEARCH_FOR_JURISPRUDENCE_PROMPT_CREATION_SYSTEM_PROMPT)
from src.app.prompts.chat.chat_prompts_v1 import (
    PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_USER_INPUT,
    PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_PROJECT_CONTEXT
)

from src.app.services.retrieval.retrieval_services_v1 import (
    RetrieverService,
    RetrieverSearchType,
)
from src.app.cosmos_db_client import CosmosDBClient
from src.app.services.rechtspraak_search_service import RechtspraakSearchService


class ChatHistoryService:
    """
    Service for managing chat history.
    """

    def __init__(self, organization_id: UUID, user_id: str, session_id: str) -> None:
        """
        Initializes the chat history service.

        Args:
            organization_id (UUID): The ID of the organization.
            user_id (str): The ID of the user.
            session_id (str): The ID of the session.
        """

        self.organization_id = organization_id
        self.user_id = user_id
        self.session_id = session_id

        self._validate_env_vars()

    @staticmethod
    def _validate_env_vars() -> None:
        """
        Validates the required environment variables for chat message history.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        required_vars = [
            os.getenv("AZURE_COSMOS_ENDPOINT"),
            os.getenv("AZURE_COSMOS_DATABASE_NAME"),
            os.getenv("AZURE_COSMOS_CONNECTION_STRING"),
        ]

        if not all(required_vars):
            raise ValueError(
                "One or more Azure Cosmos DB environment variables are not set."
            )

    def get_chat_history(self) -> BaseChatMessageHistory:
        """
        Creates and returns a chat history instance.

        Returns:
            BaseChatMessageHistory: An instance of CosmosDBChatMessageHistory.
        """

        try:
            chat_history = CosmosDBChatMessageHistory(
                cosmos_endpoint=os.getenv("AZURE_COSMOS_ENDPOINT"),
                cosmos_database=os.getenv("AZURE_COSMOS_DATABASE_NAME"),
                connection_string=os.getenv("AZURE_COSMOS_CONNECTION_STRING"),
                cosmos_container=f"org-{str(self.organization_id)}",
                user_id=self.user_id,
                session_id=self.session_id,
                ttl=int(os.getenv("AZURE_COSMOS_TTL", "604800")),
            )

            chat_history.prepare_cosmos()
            return chat_history

        except Exception as e:
            logging.error("Error preparing Azure Cosmos DB message history: %s", e)
            raise


class ChatService:
    """
    Service for handling chat operations.
    """

    def __init__(
        self,
        organization_id: UUID,
        project_id: Optional[UUID] = None,
        message_history: Optional[BaseChatMessageHistory] = None,
        cosmodb_client: Optional[CosmosDBClient] = None,
    ) -> None:
        """
        Initializes the chat service with the organization and project IDs and message history.

        Args:
            organization_id (UUID): The ID of the organization.
            project_id (Optional[UUID]): The ID of the project.
            message_history (Optional[BaseChatMessageHistory]): The message history handler.
            cosmodb_client (Optional[CosmosDBClient]: Client for retrieving user uploaded documents from db
        """

        self._organization_id = organization_id
        self._message_history = message_history
        self._project_id = project_id
        self._cosmosdb_client = cosmodb_client
        self._init_variables()
        self._init_clients()
        self._init_project_context()
        self._init_case_law_search_service()

    def _init_variables(self) -> None:
        """
        Initializes environment variables for Azure OpenAI.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        self._openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self._openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self._openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self._openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self._openai_model_name = os.getenv("AZURE_OPENAI_MODEL_NAME")
        self._openai_temperature = float(
            os.getenv("AZURE_OPENAI_TEMPERATURE_CHAT", "0.5")
        )
        self._openai_top_p = float(os.getenv("AZURE_OPENAI_TOP_P_CHAT", "0.5"))
        self._search_top_k = int(os.getenv("AZURE_SEARCH_TOP_K_RESULTS_CHAT", "5"))

        self._validate_env_vars()

    def _validate_env_vars(self) -> None:
        """
        Validates the required environment variables for Azure OpenAI.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        required_vars = [
            self._openai_api_key,
            self._openai_api_version,
            self._openai_deployment,
            self._openai_endpoint,
            self._openai_model_name,
        ]

        if not all(required_vars):
            raise ValueError(
                "One or more Azure OpenAI environment variables are not set."
            )

    def _init_clients(self) -> None:
        """
        Initializes both streaming and non-streaming Azure OpenAI clients.
        Raises:
            Exception: If an error occurs during client initialization.
        """
        try:
            # Non-streaming client
            self._chat_client = AzureChatOpenAI(
                azure_deployment=self._openai_deployment,
                api_key=self._openai_api_key,
                api_version=self._openai_api_version,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                top_p=self._openai_top_p,

            )

            # Streaming client
            self._streaming_client = AzureChatOpenAI(
                azure_deployment=self._openai_deployment,
                api_key=self._openai_api_key,
                api_version=self._openai_api_version,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                top_p=self._openai_top_p,
                streaming=True,
                max_retries=3,
                model_kwargs={"stream_options": {"include_usage": True}},
            )

            logging.info("Info: Azure OpenAI chat clients initialized.")

        except Exception as e:
            logging.error("Error initializing Azure OpenAI clients: %s", e)
            raise

    def _init_project_context(self):
        """Get the complete paragraphed text from all documents uploaded to the current project to be used as
        context in prompts to the chat"""
        if self._cosmosdb_client is not None:
            self.project_context_str = self._cosmosdb_client.create_context_for_llm_prompt()
        else:
            self.project_context_str = None

    def _init_case_law_search_service(self):
        self.case_law_search_service = RechtspraakSearchService(project_context_str=self.project_context_str)
        self.relevant_legal_cases_context_str = None

    @staticmethod
    def _create_prompt(
            context: Optional[Context] = None
    ) -> ChatPromptTemplate:
        """
        Creates the chat prompt template.

        Args:
            context (Optional[str]): Context containing project context and or jurisprudnece to guide the AI response.

        Returns:
            ChatPromptTemplate: The constructed prompt template for the chat.
        """

        #TODO include relevant legal context from wetten.nl as well
        if context.project_context:
            context_prompt = context.project_context
        else:
            context_prompt = "The user has not yet uploaded any case-specific documents."

        if context.jurisprudence_context:
            context_prompt = f"{context_prompt}{context.jurisprudence_context}.\n"
        else:
            context_prompt = f"{context_prompt}Searching for relevant case law has not been enabled.\n"

        return ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=CHAT_SERVICES_SYSTEM_PROMPT),
                MessagesPlaceholder("chat_history", optional=True),
                ("human", f"# Context \n {context_prompt} # Task \n Given this context, please answer the following question from the user: {{user_message}}"),
            ]
        )

    @staticmethod
    def _validate_chat_request(chat_request: ChatRequest) -> None:
        """
        Validates the user's input message.

        Args:
            chat_request (ChatRequest): The chat request to validate.

        Raises:
            ValueError: If the user message is empty.
        """

        if not chat_request.user_message:
            raise ValueError("Input message cannot be empty.")

    async def _create_context(self, chat_request: ChatRequest) -> Context:
        """
        Create context for prompt, combining documents uploaded to the project by the user taken directly from the
        cosmodb, and relevant laws, jurisprudence and/or other materials retrieved from the vector store

        Args:
            chat_request: ChatRequest containing the user's message

        Returns:
            Context: Combined context from both sources
        """

        # Get relevant case law if enabled and not yet performed
        if chat_request.jurisprudence and self.relevant_legal_cases_context_str is None:
            print("Searching for Relevant Case Law")

            query = \
                (f"{self.project_context_str} Also consider the following request from the user:"
                 f" {chat_request.user_message}")


            relevant_legal_cases = await self.case_law_search_service.search(query=query, return_ids_only=False)
            relevant_legal_cases_context_str = self.case_law_search_service.format_search_results_for_context_prompt(
                documents=relevant_legal_cases)
            self.relevant_legal_cases_context_str = relevant_legal_cases_context_str

        return Context(
            project_context=self.project_context_str,
            jurisprudence_context=self.relevant_legal_cases_context_str,
        )

    async def get_chat_response(self, chat_request: ChatRequest) -> ChatResponse:
        """Gets the chat response from Azure OpenAI."""
        try:
            self._validate_chat_request(chat_request)

            # Get build & invoke prompt
            chat_context = await self._create_context(chat_request)
            chat_prompt = self._create_prompt(context=chat_context)
            chat_chain = chat_prompt | self._chat_client

            # Invoke the chain with history
            chat_history = (
                self._message_history.messages if self._message_history else []
            )
            chain_result = await chat_chain.ainvoke(
                {
                    "chat_history": chat_history,
                    "user_message": chat_request.user_message,
                }
            )

            # Add messages to history
            if self._message_history:
                self._message_history.add_messages(
                    [
                        HumanMessage(content=chat_request.user_message),
                        AIMessage(content=chain_result.content),
                    ]
                )

            return ChatResponse(
                id=chain_result.id,
                organization_id=self._organization_id,
                project_id=self._project_id,
                session_id=chat_request.session_id,
                user_id=chat_request.user_id,
                user_message=chat_request.user_message,
                content=chain_result.content,
                prompt_tokens=chain_result.response_metadata["token_usage"][
                    "prompt_tokens"
                ],
                completion_tokens=chain_result.response_metadata["token_usage"][
                    "completion_tokens"
                ],
                total_tokens=chain_result.response_metadata["token_usage"][
                    "total_tokens"
                ],
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting chat response: %s", e)
            raise e

    async def astream_chat_response(
        self, chat_request: ChatRequest
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Streams chat responses with token usage information.

        Args:
            chat_request (ChatRequest): The chat request containing the user message.

        Yields:
            StreamingChunk: Contains content chunk and metadata

        Raises:
            ValueError: If the user message is empty.
            Exception: If an error occurs during response generation.
        """
        try:
            self._validate_chat_request(chat_request)

            chat_context = await self._create_context(chat_request)

            chat_prompt = self._create_prompt(context=chat_context)

            # Create chain with streaming client
            chat_chain = chat_prompt | self._streaming_client
            response_content = []
            final_chunk = None
            # Stream the content
            async for chunk in chat_chain.astream(
                {
                    "chat_history": self._message_history.messages,
                    "user_message": chat_request.user_message,
                }
            ):
                if chunk.usage_metadata:
                    final_chunk = chunk
                    break
                content = chunk.content
                response_content.append(content)

                yield StreamingChunk(content=content, is_final=False)

            # Add messages to history
            self._message_history.add_messages(
                [
                    HumanMessage(content=chat_request.user_message),
                    AIMessage(content="".join(response_content)),
                ]
            )

            # Create base metadata
            metadata = {
                "id": final_chunk.id if final_chunk else f"run-{uuid.uuid4()}-0",
                "organization_id": str(self._organization_id),
                "project_id": str(self._project_id) if self._project_id else None,
                "session_id": chat_request.session_id,
                "user_id": chat_request.user_id,
                "user_message": chat_request.user_message,
            }

            # If we received usage metadata, include it in the final chunk
            if final_chunk and final_chunk.usage_metadata:
                metadata.update({
                    "prompt_tokens": final_chunk.usage_metadata["input_tokens"],
                    "completion_tokens": final_chunk.usage_metadata["output_tokens"],
                    "total_tokens": final_chunk.usage_metadata["total_tokens"],
                })
            else:
                # If no usage metadata was received, provide default values
                logging.warning("No usage metadata received from OpenAI API")
                metadata.update({
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                })

            # Send final chunk with metadata
            yield StreamingChunk(
                content="",
                is_final=True,
                metadata=metadata,
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting chat response: %s", e)
            raise e
