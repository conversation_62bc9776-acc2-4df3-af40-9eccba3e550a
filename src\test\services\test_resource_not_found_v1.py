import os

import pytest
from azure.core.exceptions import ResourceNotFoundError
from uuid import UUID

from pytest_mock import Mo<PERSON><PERSON>ixture

from src.app.models.projects.completion_models_v1 import (
    CompletionType,
    CompletionOptions,
)
from src.app.services.retrieval.retrieval_services_v1 import RetrieverService
from src.app.services.completion.completion_services_v1 import (
    DocumentCompletionService,
    ProjectCompletionService,
)


class TestResourceNotFound:
    @pytest.fixture
    def organization_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture
    def project_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture
    def document_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture(autouse=True)
    def setup_environment(self):
        """Setup environment variables needed for testing"""
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"
        os.environ["AZURE_OPENAI_API_VERSION"] = "2023-05-15"
        os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"] = "test-deployment"
        os.environ["AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME"] = "test-embeddings"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com"
        os.environ["AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_DATASOURCE_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_INDEX_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_INDEXER_PREFIX"] = "test-"
        os.environ["AZURE_STORAGE_ACCOUNT_PATH_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_ADMIN_KEY"] = "test-search-key"
        os.environ["AZURE_SEARCH_ENDPOINT"] = "https://test.search.windows.net"
        os.environ["AZURE_SEARCH_TOP_K_RESULTS"] = "50"
        os.environ["AZURE_STORAGE_ACCOUNT_KEY"] = "test-storage-key"
        os.environ["AZURE_STORAGE_ACCOUNT_NAME"] = "teststorage"
        yield
        # Cleanup
        del os.environ["AZURE_OPENAI_API_KEY"]
        del os.environ["AZURE_OPENAI_API_VERSION"]
        del os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"]
        del os.environ["AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME"]
        del os.environ["AZURE_OPENAI_ENDPOINT"]
        del os.environ["AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX"]
        del os.environ["AZURE_SEARCH_DATASOURCE_PREFIX"]
        del os.environ["AZURE_SEARCH_INDEX_PREFIX"]
        del os.environ["AZURE_SEARCH_INDEXER_PREFIX"]
        del os.environ["AZURE_STORAGE_ACCOUNT_PATH_PREFIX"]
        del os.environ["AZURE_SEARCH_ADMIN_KEY"]
        del os.environ["AZURE_SEARCH_ENDPOINT"]
        del os.environ["AZURE_SEARCH_TOP_K_RESULTS"]
        del os.environ["AZURE_STORAGE_ACCOUNT_KEY"]
        del os.environ["AZURE_STORAGE_ACCOUNT_NAME"]

    @pytest.fixture
    def retriever_service(self, mocker: MockerFixture, organization_id):
        # Mock Azure clients
        mock_embeddings = mocker.patch(
            "langchain_openai.AzureOpenAIEmbeddings", autospec=True
        )
        mock_embeddings.return_value.embed_query.return_value = [0.1] * 1536
        mock_azure_search = mocker.patch(
            "langchain_community.vectorstores.azuresearch.AzureSearch"
        )

        from src.app.services.retrieval.retrieval_services_v1 import RetrieverService

        return RetrieverService(organization_id=organization_id)

    def test_retriever_service_nonexistent_project(
        self, organization_id, project_id, mocker
    ):
        """Test initialization with non-existent project"""
        mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.SearchIndexClient.get_index",
            side_effect=ResourceNotFoundError("Index not found"),
        )

        with pytest.raises(ResourceNotFoundError) as exc_info:
            RetrieverService(organization_id=organization_id, project_id=project_id)

        assert f"Vector store for project {project_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_document_completion_service_not_found(
        self, organization_id, project_id, mocker
    ):
        """Test document completion service with non-existent documents"""
        mock_cosmosdb = mocker.Mock()
        mock_retriever = mocker.Mock()
        mock_retriever.search_project.side_effect = ResourceNotFoundError(
            "Documents not found"
        )

        service = DocumentCompletionService(cosmodb_client=mock_cosmosdb,  retriever_service=mock_retriever)

        with pytest.raises(ResourceNotFoundError):
            await service.get_completion(
                "test query", completion_type=CompletionType.DOCUMENT_SUMMARY
            )

    @pytest.mark.asyncio
    async def test_project_completion_service_not_found(
        self, organization_id, project_id, mocker
    ):
        """Test project completion service with non-existent project"""
        mock_cosmos_db = mocker.Mock()
        mock_retriever = mocker.Mock()
        mock_retriever.search_project.side_effect = ResourceNotFoundError(
            "Project not found"
        )

        service = ProjectCompletionService(cosmodb_client=mock_cosmos_db, retriever_service=mock_retriever)

        completion_options = CompletionOptions(
            CompletionType.DOCUMENT_SUMMARY,
            params={
                "claimant": "claimant",
                "defendant": "defendant",
            },
        )

        with pytest.raises(ResourceNotFoundError):
            await service.get_completion(
                "test query", completion_options=completion_options
            )

    def test_vector_store_initialization_failure(
        self, organization_id, project_id, mocker
    ):
        """Test vector store initialization failure"""
        mocker.patch(
            "azure.search.documents.indexes.SearchIndexClient",
            side_effect=ResourceNotFoundError("Search service not found"),
        )

        with pytest.raises(ResourceNotFoundError) as exc_info:
            RetrieverService(organization_id=organization_id, project_id=project_id)

        assert "Vector store" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_document_retrieval_failure(
        self, organization_id, project_id, document_id, mocker
    ):
        """Test document retrieval failure"""
        mock_retriever = mocker.Mock()
        mock_retriever.retrieve_project.side_effect = ResourceNotFoundError(
            "Document not found"
        )

        with pytest.raises(ResourceNotFoundError) as exc_info:
            await mock_retriever.retrieve_project("test query")

        assert "Document not found" in str(exc_info.value)
